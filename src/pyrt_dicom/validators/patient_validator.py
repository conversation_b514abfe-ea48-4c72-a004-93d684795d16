"""Patient Module DICOM validation - PS3.3 C.7.1.1"""

from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..enums.patient_enums import PatientSex, ResponsiblePersonRole


class PatientValidator:
    """Validator for DICOM Patient Module (PS3.3 C.7.1.1)."""
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig = None) -> dict[str, list[str]]:
        """Validate Patient Module requirements on any pydicom Dataset.
        
        Args:
            dataset: pydicom Dataset to validate
            config: Validation configuration options
            
        Returns:
            Dict with 'errors' and 'warnings' lists
        """
        config = config or ValidationConfig()
        result = {"errors": [], "warnings": []}
        
        # Validate Type 1C conditional requirements
        if config.validate_conditional_requirements:
            result = PatientValidator._validate_conditional_requirements(dataset, result)
        
        # Validate enumerated values
        if config.check_enumerated_values:
            result = PatientValidator._validate_enumerated_values(dataset, result)
        
        # Validate sequence structures
        if config.validate_sequences:
            result = PatientValidator._validate_sequence_requirements(dataset, result)
        
        return result
    
    @staticmethod
    def _validate_conditional_requirements(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate Type 1C and 2C conditional requirements."""
        
        # Type 1C: Alternative Calendar requirement
        has_alt_birth = hasattr(dataset, 'PatientsBirthDateInAlternativeCalendar')
        has_alt_death = hasattr(dataset, 'PatientsDeathDateInAlternativeCalendar')
        if has_alt_birth or has_alt_death:
            if not hasattr(dataset, 'PatientsAlternativeCalendar'):
                result["errors"].append(
                    "Patient's Alternative Calendar (0010,0035) is required when "
                    "alternative calendar birth or death dates are present"
                )
        
        # Type 1C: Non-human organism species requirement
        is_non_human = (hasattr(dataset, 'PatientSpeciesDescription') or 
                       hasattr(dataset, 'PatientSpeciesCodeSequence'))
        
        if is_non_human:
            # Either species description OR species code sequence required
            has_species_desc = hasattr(dataset, 'PatientSpeciesDescription')
            has_species_code = hasattr(dataset, 'PatientSpeciesCodeSequence')
            if not has_species_desc and not has_species_code:
                result["errors"].append(
                    "Either Patient Species Description (0010,2201) or "
                    "Patient Species Code Sequence (0010,2202) is required for non-human organisms"
                )
            
            # Type 2C: Breed requirements for non-human
            breed_desc = getattr(dataset, 'PatientBreedDescription', '')
            breed_seq = getattr(dataset, 'PatientBreedCodeSequence', [])
            if not breed_desc and not breed_seq:
                result["errors"].append(
                    "Patient Breed Description (0010,2292) is required for non-human organisms "
                    "when Patient Breed Code Sequence (0010,2293) is empty"
                )
            
            # Type 2C: Responsible person OR organization required for non-human
            resp_person = getattr(dataset, 'ResponsiblePerson', '')
            resp_org = getattr(dataset, 'ResponsibleOrganization', '')
            if not resp_person and not resp_org:
                result["errors"].append(
                    "Either Responsible Person (0010,2297) or Responsible Organization (0010,2299) "
                    "is required for non-human organisms"
                )
        
        # Type 1C: Responsible Person Role requirement
        resp_person = getattr(dataset, 'ResponsiblePerson', '')
        if resp_person and not hasattr(dataset, 'ResponsiblePersonRole'):
            result["errors"].append(
                "Responsible Person Role (0010,2298) is required when "
                "Responsible Person (0010,2297) is present and has a value"
            )
        
        # Type 1C: De-identification method requirement
        identity_removed = getattr(dataset, 'PatientIdentityRemoved', '')
        if identity_removed == "YES":
            has_method = hasattr(dataset, 'DeIdentificationMethod')
            has_method_seq = hasattr(dataset, 'DeIdentificationMethodCodeSequence')
            if not has_method and not has_method_seq:
                result["errors"].append(
                    "Either De-identification Method (0012,0063) or "
                    "De-identification Method Code Sequence (0012,0064) is required "
                    "when Patient Identity Removed (0012,0062) is YES"
                )
        
        return result
    
    @staticmethod
    def _validate_enumerated_values(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate enumerated values against DICOM specifications."""
        
        # Patient's Sex (0010,0040)
        patients_sex = getattr(dataset, 'PatientsSex', '')
        if patients_sex:
            BaseValidator.validate_enumerated_value(
                patients_sex, ["M", "F", "O"], 
                "Patient's Sex (0010,0040)", result
            )
        
        # Quality Control Subject (0010,0200)
        quality_control = getattr(dataset, 'QualityControlSubject', '')
        if quality_control:
            BaseValidator.validate_enumerated_value(
                quality_control, ["YES", "NO"],
                "Quality Control Subject (0010,0200)", result
            )
        
        # Type of Patient ID (0010,0022)
        type_of_id = getattr(dataset, 'TypeOfPatientID', '')
        if type_of_id:
            BaseValidator.validate_enumerated_value(
                type_of_id, ["TEXT", "RFID", "BARCODE"],
                "Type of Patient ID (0010,0022)", result
            )
        
        # Patient Identity Removed (0012,0062)
        identity_removed = getattr(dataset, 'PatientIdentityRemoved', '')
        if identity_removed:
            BaseValidator.validate_enumerated_value(
                identity_removed, ["YES", "NO"],
                "Patient Identity Removed (0012,0062)", result
            )
        
        # Responsible Person Role (0010,2298)
        resp_role = getattr(dataset, 'ResponsiblePersonRole', '')
        if resp_role:
            valid_roles = [role.value for role in ResponsiblePersonRole]
            BaseValidator.validate_enumerated_value(
                resp_role, valid_roles,
                "Responsible Person Role (0010,2298)", result
            )
        
        # Strain Nomenclature (0010,0213)
        strain_nom = getattr(dataset, 'StrainNomenclature', '')
        if strain_nom:
            BaseValidator.validate_enumerated_value(
                strain_nom, ["MGI_2013"],
                "Strain Nomenclature (0010,0213)", result
            )
        
        return result
    
    @staticmethod
    def _validate_sequence_requirements(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate sequence structure requirements."""
        
        # Other Patient IDs Sequence - each item needs Patient ID and Type
        other_ids_seq = getattr(dataset, 'OtherPatientIDsSequence', [])
        for i, item in enumerate(other_ids_seq):
            if not item.get('PatientID'):
                result["errors"].append(f"Other Patient IDs Sequence item {i}: Patient ID (0010,0020) is required")
            if not item.get('TypeOfPatientID'):
                result["errors"].append(f"Other Patient IDs Sequence item {i}: Type of Patient ID (0010,0022) is required")
        
        # Strain Stock Sequence - each item needs stock number, source, and registry
        strain_stock_seq = getattr(dataset, 'StrainStockSequence', [])
        for i, item in enumerate(strain_stock_seq):
            if not item.get('StrainStockNumber'):
                result["errors"].append(f"Strain Stock Sequence item {i}: Strain Stock Number (0010,0214) is required")
            if not item.get('StrainSource'):
                result["errors"].append(f"Strain Stock Sequence item {i}: Strain Source (0010,0217) is required")
            if not item.get('StrainSourceRegistryCodeSequence'):
                result["errors"].append(f"Strain Stock Sequence item {i}: Strain Source Registry Code Sequence (0010,0215) is required")
        
        # Genetic Modifications Sequence - each item needs description and nomenclature
        genetic_mod_seq = getattr(dataset, 'GeneticModificationsSequence', [])
        for i, item in enumerate(genetic_mod_seq):
            if not item.get('GeneticModificationsDescription'):
                result["errors"].append(f"Genetic Modifications Sequence item {i}: Genetic Modifications Description (0010,0222) is required")
            if not item.get('GeneticModificationsNomenclature'):
                result["errors"].append(f"Genetic Modifications Sequence item {i}: Genetic Modifications Nomenclature (0010,0223) is required")
        
        return result