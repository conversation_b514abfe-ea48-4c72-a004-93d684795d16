"""
RT Series Module - DICOM PS3.3 C.8.8.1

The RT Series Module contains attributes that identify and describe 
the RT Series performed as part of a Study.
"""
from typing import Optional, List, Dict, Any
from datetime import datetime, date
from pydicom.valuerep import PersonName
from .base_module import BaseModule
from ..enums.series_enums import Modality
from ..validators.base_validator import ValidationConfig


class RTSeriesModule(BaseModule):
    """RT Series Module implementation for DICOM PS3.3 C.8.8.1.
    
    Inherits from BaseModule to provide native DICOM data handling.
    Contains attributes that identify and describe the RT Series performed as part of a Study.
    
    Usage:
        # Create with required elements
        rt_series = RTSeriesModule.from_required_elements(
            modality=Modality.RTDOSE,
            series_instance_uid="*******.*******.9.10",
            series_number=1
        )
        
        # Add optional elements
        rt_series.with_optional_elements(
            series_date="20240101",
            series_time="120000",
            series_description="RT Dose Distribution"
        )
        
        # Add operator information
        rt_series.with_operator_information(
            operators_name="<PERSON>^<PERSON>",
            performing_physicians_name="<PERSON><PERSON><PERSON>"
        )
        
        # Add RT-specific information
        rt_series.with_rt_specific_elements(
            treatment_machine_name="LINAC_001",
            referenced_frame_of_reference_sequence=[{
                'FrameOfReferenceUID': '*******.*******.9.11'
            }]
        )
        
        # Validate
        result = rt_series.validate()
    """

    @classmethod
    def from_required_elements(
        cls,
        modality: str | Modality,
        series_instance_uid: str,
        series_number: Optional[int] = None
    ) -> 'RTSeriesModule':
        """Create RTSeriesModule from all required (Type 1 and Type 2) data elements.
        
        Args:
            modality (str | Modality): Type of equipment that originally acquired the data (0008,0060) Type 1
            series_instance_uid (str): Unique identifier for the Series (0020,000E) Type 1
            series_number (int | None): Series number (0020,0011) Type 2
            
        Returns:
            RTSeriesModule: New dataset instance with required data elements set
        """
        instance = cls()
        instance.Modality = instance._format_enum_value(modality)
        instance.SeriesInstanceUID = series_instance_uid
        instance.SeriesNumber = series_number
        return instance
    
    def with_optional_elements(
        self,
        series_date: Optional[str | datetime | date] = None,
        series_time: Optional[str | datetime] = None,
        performing_physicians_name: Optional[str | PersonName] = None,
        performing_physician_identification_sequence: Optional[List[Dict[str, Any]]] = None,
        operators_name: Optional[str | PersonName] = None,
        operator_identification_sequence: Optional[List[Dict[str, Any]]] = None,
        referenced_performed_procedure_step_sequence: Optional[List[Dict[str, Any]]] = None,
        related_series_sequence: Optional[List[Dict[str, Any]]] = None,
        series_description: Optional[str] = None,
        series_description_code_sequence: Optional[List[Dict[str, Any]]] = None,
        protocol_name: Optional[str] = None,
        body_part_examined: Optional[str] = None
    ) -> 'RTSeriesModule':
        """Add optional (Type 3) data elements without conditional requirements.
        
        Args:
            series_date (str | datetime | date | None): Date the Series started (0008,0021) Type 3
            series_time (str | datetime | None): Time the Series started (0008,0031) Type 3
            performing_physicians_name (str | PersonName | None): Name of physician performing the Series (0008,1050) Type 3
            performing_physician_identification_sequence (list[dict[str, any]] | None): Performing physician ID (0008,1052) Type 3
            operators_name (str | PersonName | None): Name of operator of equipment (0008,1070) Type 3
            operator_identification_sequence (list[dict[str, any]] | None): Operator identification (0008,1072) Type 3
            referenced_performed_procedure_step_sequence (list[dict[str, any]] | None): Reference to performed procedure step (0008,1111) Type 3
            related_series_sequence (list[dict[str, any]] | None): Related Series (0008,1140) Type 3
            series_description (str | None): User provided description of the Series (0008,103E) Type 3
            series_description_code_sequence (list[dict[str, any]] | None): Coded series description (0008,103F) Type 3
            protocol_name (str | None): User or equipment generated identifier (0018,1030) Type 3
            body_part_examined (str | None): Text description of part of body examined (0018,0015) Type 3
            
        Returns:
            RTSeriesModule: Self with optional elements added
        """
        if series_date is not None:
            self.SeriesDate = self._format_date_value(series_date)
        if series_time is not None:
            self.SeriesTime = self._format_time_value(series_time)
        self._set_attribute_if_not_none('PerformingPhysiciansName', performing_physicians_name)
        self._set_attribute_if_not_none('PerformingPhysicianIdentificationSequence', performing_physician_identification_sequence)
        self._set_attribute_if_not_none('OperatorsName', operators_name)
        self._set_attribute_if_not_none('OperatorIdentificationSequence', operator_identification_sequence)
        self._set_attribute_if_not_none('ReferencedPerformedProcedureStepSequence', referenced_performed_procedure_step_sequence)
        self._set_attribute_if_not_none('RelatedSeriesSequence', related_series_sequence)
        self._set_attribute_if_not_none('SeriesDescription', series_description)
        self._set_attribute_if_not_none('SeriesDescriptionCodeSequence', series_description_code_sequence)
        self._set_attribute_if_not_none('ProtocolName', protocol_name)
        self._set_attribute_if_not_none('BodyPartExamined', body_part_examined)
        return self
    
    def with_operator_information(
        self,
        operators_name: Optional[str | PersonName] = None,
        operator_identification_sequence: Optional[List[Dict[str, Any]]] = None,
        performing_physicians_name: Optional[str | PersonName] = None,
        performing_physician_identification_sequence: Optional[List[Dict[str, Any]]] = None
    ) -> 'RTSeriesModule':
        """Add operator and performing physician information.
        
        Args:
            operators_name (str | PersonName | None): Name of operator of equipment (0008,1070) Type 3
            operator_identification_sequence (list[dict[str, any]] | None): Operator identification (0008,1072) Type 3
            performing_physicians_name (str | PersonName | None): Name of physician performing the Series (0008,1050) Type 3
            performing_physician_identification_sequence (list[dict[str, any]] | None): Performing physician ID (0008,1052) Type 3
            
        Returns:
            RTSeriesModule: Self with operator information elements added
        """
        self._set_attribute_if_not_none('OperatorsName', operators_name)
        self._set_attribute_if_not_none('OperatorIdentificationSequence', operator_identification_sequence)
        self._set_attribute_if_not_none('PerformingPhysiciansName', performing_physicians_name)
        self._set_attribute_if_not_none('PerformingPhysicianIdentificationSequence', performing_physician_identification_sequence)
        return self
    
    def with_rt_specific_elements(
        self,
        treatment_machine_name: Optional[str] = None,
        referenced_frame_of_reference_sequence: Optional[List[Dict[str, Any]]] = None,
        position_reference_indicator: Optional[str] = None
    ) -> 'RTSeriesModule':
        """Add RT-specific elements.
        
        Args:
            treatment_machine_name (str | None): User-defined name for treatment machine (300A,00B2) Type 3
            referenced_frame_of_reference_sequence (list[dict[str, any]] | None): Frame of reference (3006,0010) Type 3
            position_reference_indicator (str | None): Part of body used as reference (0020,1040) Type 2
            
        Returns:
            RTSeriesModule: Self with RT-specific elements added
        """
        self._set_attribute_if_not_none('TreatmentMachineName', treatment_machine_name)
        self._set_attribute_if_not_none('ReferencedFrameOfReferenceSequence', referenced_frame_of_reference_sequence)
        self._set_attribute_if_not_none('PositionReferenceIndicator', position_reference_indicator)
        return self

    @staticmethod
    def create_referenced_frame_of_reference_item(
        frame_of_reference_uid: str,
        rt_referenced_study_sequence: Optional[List[Dict[str, Any]]] = None,
        rt_referenced_series_sequence: Optional[List[Dict[str, Any]]] = None
    ) -> Dict[str, Any]:
        """Create an item for Referenced Frame of Reference Sequence (3006,0010).

        Args:
            frame_of_reference_uid (str): Frame of Reference UID (0020,0052) Type 1
            rt_referenced_study_sequence (list[dict[str, any]] | None): Referenced studies (3006,0012) Type 3
            rt_referenced_series_sequence (list[dict[str, any]] | None): Referenced series (3006,0014) Type 3

        Returns:
            Dict[str, Any]: Sequence item with frame of reference information
        """
        item = {
            'FrameOfReferenceUID': frame_of_reference_uid
        }
        if rt_referenced_study_sequence is not None:
            item['RTReferencedStudySequence'] = rt_referenced_study_sequence
        if rt_referenced_series_sequence is not None:
            item['RTReferencedSeriesSequence'] = rt_referenced_series_sequence
        return item

    @staticmethod
    def create_related_series_item(
        study_instance_uid: str,
        series_instance_uid: str,
        purpose_of_reference_code_sequence: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Create an item for Related Series Sequence (0008,1140).

        Args:
            study_instance_uid (str): Study Instance UID of related series
            series_instance_uid (str): Series Instance UID of related series
            purpose_of_reference_code_sequence (list[dict[str, any]]): Purpose of reference

        Returns:
            Dict[str, Any]: Sequence item with related series information
        """
        return {
            'StudyInstanceUID': study_instance_uid,
            'SeriesInstanceUID': series_instance_uid,
            'PurposeOfReferenceCodeSequence': purpose_of_reference_code_sequence
        }

    @property
    def has_operator_info(self) -> bool:
        """Check if operator information is present.

        Returns:
            bool: True if operator-related elements are present
        """
        return any(hasattr(self, attr) for attr in [
            'OperatorsName', 'OperatorIdentificationSequence'
        ])

    @property
    def has_performing_physician_info(self) -> bool:
        """Check if performing physician information is present.

        Returns:
            bool: True if performing physician elements are present
        """
        return any(hasattr(self, attr) for attr in [
            'PerformingPhysiciansName', 'PerformingPhysicianIdentificationSequence'
        ])

    @property
    def has_rt_specific_info(self) -> bool:
        """Check if RT-specific information is present.

        Returns:
            bool: True if RT-specific elements are present
        """
        return any(hasattr(self, attr) for attr in [
            'TreatmentMachineName', 'ReferencedFrameOfReferenceSequence', 'PositionReferenceIndicator'
        ])

    def validate(self, config: Optional[ValidationConfig] = None) -> Dict[str, List[str]]:
        """Validate this RT Series Module instance.

        Args:
            config (ValidationConfig | None): Optional validation configuration

        Returns:
            Dict with 'errors' and 'warnings' lists
        """
        # TODO: Implement specific validator for RTSeriesModule
        # For now, return basic validation
        result = {"errors": [], "warnings": []}

        # Check Type 1 requirements
        if not hasattr(self, 'Modality') or not self.Modality:
            result["errors"].append("Modality (0008,0060) is required (Type 1)")

        if not hasattr(self, 'SeriesInstanceUID') or not self.SeriesInstanceUID:
            result["errors"].append("Series Instance UID (0020,000E) is required (Type 1)")

        # Check that modality is RT-related
        if hasattr(self, 'Modality'):
            rt_modalities = ['RTIMAGE', 'RTDOSE', 'RTSTRUCT', 'RTPLAN', 'RTRECORD']
            if self.Modality not in rt_modalities:
                result["warnings"].append(f"Modality '{self.Modality}' is not typically used for RT Series")

        return result
