# Module Implementation Plan

This document outlines the implementation strategy for DICOM modules with a focus on user-friendly IntelliSense support and intuitive APIs. All modules inherit from BaseModule and the PatientModule serves as the exemplar for all future module implementations.

## BaseModule Abstract Base Class

**All DICOM modules inherit from BaseModule which provides:**

### 🏗️ Core Infrastructure
- **pydicom.Dataset Inheritance**: Native DICOM data handling through Dataset inheritance
- **Abstract Method Enforcement**: Ensures all modules implement `from_required_elements()`
- **Validation Framework**: Common validation interface with structured error/warning reporting
- **Helper Methods**: Common utilities for date/time formatting, enum handling, and conditional validation

### 🛠️ Helper Methods Available to All Modules
- **`_set_attribute_if_not_none(attr_name, value)`**: Set DICOM attribute only if value is not None
- **`_validate_conditional_requirement(condition, required_values, error_message)`**: Validate Type 1C/2C requirements
- **`_format_date_value(date_value)`**: Format dates to DICOM DA format (YYYYMMDD)
- **`_format_time_value(time_value)`**: Format times to DICOM TM format (HHMMSS)
- **`_format_enum_value(enum_value)`**: Extract string value from enum objects

### 🔍 Common Properties
- **`module_name`**: Get the module class name
- **`has_data`**: Check if module contains any DICOM data elements
- **`get_element_count()`**: Get number of DICOM data elements
- **`get_element_tags()`**: Get list of DICOM tags present in module

### ⚠️ Important Constraints
- **No Save Methods**: Modules are NOT intended to be saved directly - file operations are handled by IOD classes
- **No File Metadata**: Modules contain only DICOM data elements, not file metadata
- **Abstract Base**: BaseModule cannot be instantiated directly - only concrete module subclasses

## Core User Experience Features

**PatientModule Demonstrates Clean IntelliSense Through Method-Based API:**

### 🎯 Clean IntelliSense Design
- **Method-Only Interface**: No direct DICOM attribute access to avoid IntelliSense clutter
- **Logical Method Grouping**: Clear separation of construction, configuration, and validation methods
- **Type-Safe Parameters**: Full IDE autocomplete with parameter types and enum support
- **Method Chaining**: Fluent API enables `from_required_elements().with_optional_elements()`

### 🏗️ DICOM Type-Based Parameter Design
- **Type 1 as Required Args**: `from_required_elements(type1_param)` - user must provide value
- **Type 2 as Kwargs with Defaults**: `from_required_elements(type2_param="")` - required but can be empty
- **Type 3 as Optional**: `with_optional_elements(type3_param=None)` - truly optional elements
- **Conditional Types**: `with_[...]()` handles Type 1C/2C based on context

### 🧩 Method Categories by DICOM Type
- **`from_required_elements()`**: All Type 1 (args) + Type 2 (kwargs with empty defaults)
- **`with_optional_elements()`**: All Type 3 elements (kwargs with None defaults)  
- **`with_[...]()`**: Type 1C/2C elements with validation logic
- **`create_*_item()`**: Static helpers for sequence item creation

### 🔍 Logical Properties & Validation
- **State Checks**: `is_human`, `is_non_human`, `is_deidentified`, `has_alternative_calendar_dates`
- **Validation**: `validate()` returns structured error/warning dictionary

## Module Implementation Template

**All future modules must inherit from BaseModule and follow the PatientModule DICOM Type-based design:**

```python
from .base_module import BaseModule

class NewModule(BaseModule):
    """Module description - DICOM PS3.3 reference.

    Usage:
        module = NewModule.from_required_elements(
            type1_required_param,                    # Type 1: User must provide
            type2_param="",                         # Type 2: Required but can be empty
            type2_another=""
        ).with_optional_elements(
            type3_param=None,                       # Type 3: Optional
            type3_another=None
        ).with_conditional_group(
            type1c_param,                           # Type 1C: Required if condition met
            type2c_param=""                         # Type 2C: Required but empty if condition met
        )
    """
    
    @classmethod
    def from_required_elements(
        cls, 
        # Type 1 elements as positional args (user MUST provide value)
        required_param: str,
        # Type 2 elements as kwargs with empty string defaults (required but can be empty)
        optional_but_present_param: str = "",
        another_type2_param: str = ""
    ) -> 'NewModule':
        """Create module with all required (Type 1 and Type 2) elements.
        
        Args:
            required_param: Description (tag) Type 1
            optional_but_present_param: Description (tag) Type 2
            another_type2_param: Description (tag) Type 2
        """
        instance = cls()
        # Set DICOM attributes using BaseModule helper methods
        instance.RequiredParam = required_param
        instance.OptionalButPresentParam = optional_but_present_param
        instance.AnotherType2Param = another_type2_param
        return instance
    
    def with_optional_elements(
        self,
        # Type 3 elements as kwargs with None defaults (truly optional)
        optional_param: str | None = None,
        another_optional: str | None = None
    ) -> 'NewModule':
        """Add optional (Type 3) elements."""
        # Use BaseModule helper methods
        self._set_attribute_if_not_none('OptionalParam', optional_param)
        self._set_attribute_if_not_none('AnotherOptional', another_optional)
        return self
    
    def with_conditional_group(
        self,
        # Type 1C/2C elements with validation
        conditional_param: str,
        conditional_optional: str = ""
    ) -> 'NewModule':
        """Add conditional elements with required validation logic."""
        # Use BaseModule validation helper
        self._validate_conditional_requirement(
            some_condition,  # Boolean condition
            [conditional_param],  # Required values when condition is true
            "conditional_param required when condition X is true"
        )

        self.ConditionalParam = conditional_param
        self.ConditionalOptional = conditional_optional
        return self
    
    @staticmethod
    def create_sequence_item(
        required_item_param: str,
        optional_item_param: str | None = None
    ) -> dict[str, any]:
        """Create sequence items for complex structures."""
        item = {'RequiredItemParam': required_item_param}
        if optional_item_param is not None:
            item['OptionalItemParam'] = optional_item_param
        return item
    
    @property
    def is_configured(self) -> bool:
        """Logical state check based on internal data."""
        return hasattr(self, 'RequiredParam')
    
    @property  
    def has_optional_data(self) -> bool:
        """Check if optional elements are present."""
        return hasattr(self, 'OptionalParam')
    
    def validate(self, config: ValidationConfig = None) -> dict[str, list[str]]:
        """Validate module data against DICOM standard."""
        return ModuleValidator.validate(self, config)
```

## Key Design Principles

### 🎯 Module Success Criteria
1. **Clean IntelliSense**: Only methods and properties visible, no raw DICOM attributes
2. **DICOM Type Clarity**: Type 1 (args), Type 2 (kwargs=""), Type 3 (kwargs=None), Conditional (validated)
3. **Method-Based API**: All data setting through explicit named parameter methods
4. **Logical Properties**: `is_*` and `has_*` properties for common state checks
5. **Validation Integration**: Built-in validation with structured error/warning reporting

### 🏗️ Implementation Requirements
- **Inherit from BaseModule**: All modules inherit from BaseModule (which inherits from pydicom.Dataset)
- **Named Parameters**: Every DICOM element visible as explicit method parameter
- **Type Safety**: Full type hints for IDE support and runtime validation
- **Error Handling**: Clear validation errors with DICOM tag references
- **Documentation**: Google-style docstrings with DICOM references
- **Helper Methods**: Use BaseModule helper methods for common patterns (date/time formatting, enum handling, etc.)

### ✅ Validation Standards
- **On-Demand**: `validate()` method returns `{"errors": [], "warnings": []}`
- **Configurable**: `ValidationConfig` parameter for customization
- **Structured**: No exceptions during validation

## Module Categories

**All modules inherit from BaseModule and follow the PatientModule pattern with method-based APIs:**

### 🏥 Patient Information Modules
- [x] **PatientModule** (C.7.1.1) - ✅ Complete exemplar implementation with BaseModule inheritance
- [x] **ClinicalTrialSubjectModule** (C.7.1.3) - ✅ Complete with conditional validation for subject identification
- [x] **PatientStudyModule** (C.7.2.2) - ✅ Complete with comprehensive patient study attributes

### 📋 Study & Series Modules
- [x] **GeneralStudyModule** (C.7.2.1) - ✅ Complete with comprehensive study identification and physician info
- [x] **ClinicalTrialStudyModule** (C.7.2.3) - ✅ Complete with temporal event and consent handling
- [x] **GeneralSeriesModule** (C.7.3.1) - ✅ Complete with positioning, operator, and pixel value info
- [x] **RTSeriesModule** (C.8.8.1) - ✅ Complete with RT-specific elements and frame references
- [x] **ClinicalTrialSeriesModule** (C.7.3.2) - ✅ Complete with coordinating center and series identification

### 🖼️ Image & Spatial Modules
- [ ] **GeneralImageModule** (C.7.6.1) - General image information
- [ ] **ImagePlaneModule** (C.7.6.2) - Image geometry and orientation
- [ ] **ImagePixelModule** (C.7.6.3) - Pixel data characteristics
- [ ] **MultiFrameModule** (C.7.6.6) - Multi-frame image data
- [ ] **FrameOfReferenceModule** (C.7.4.1) - Spatial reference frame
- [ ] **CineModule** - Cine image information
- [ ] **OverlayPlaneModule** - Overlay plane data

### ⚡ Radiotherapy Modules
- [ ] **RTDoseModule** (C.8.8.3) - Dose distribution data
- [ ] **RTDVHModule** (C.8.8.4) - Dose-volume histogram
- [ ] **RTImageModule** (C.8.8.2) - RT image information
- [ ] **RTGeneralPlanModule** (C.8.8.9) - Basic RT plan information
- [ ] **StructureSetModule** (C.8.8.5) - Structure set information
- [ ] **RTBeamsModule** - RT beam information
- [ ] **RTBrachyApplicationSetupsModule** - Brachytherapy application setups
- [ ] **RTFractionSchemeModule** - RT fraction scheme
- [ ] **RTPatientSetupModule** - RT patient setup
- [ ] **RTPrescriptionModule** - RT prescription
- [ ] **RTROIObservationsModule** - RT ROI observations
- [ ] **RTToleranceTablesModule** - RT tolerance tables
- [ ] **ROIContourModule** - ROI contour data

### 🔧 Equipment & Common Modules
- [ ] **GeneralEquipmentModule** (C.7.5.1) - Equipment/device information
- [ ] **SOPCommonModule** (C.12.1) - SOP instance identification
- [ ] **CommonInstanceReferenceModule** (C.12.2) - Instance references
- [ ] **DeviceModule** - Device information
- [ ] **GeneralReferenceModule** - General reference data

### 🔬 Specialized Modules
- [ ] **CTImageModule** - CT image specific data
- [ ] **MultiEnergyCtImageModule** - Multi-energy CT image data
- [ ] **ContrastBolusModule** - Contrast bolus information
- [ ] **GeneralAcquisitionModule** - General acquisition parameters
- [ ] **ModalityLutModule** - Modality LUT data
- [ ] **VoiLutModule** - VOI LUT data
- [ ] **ApprovalModule** - Approval information
- [ ] **FrameExtractionModule** - Frame extraction data
- [ ] **EnhancedPatientOrientationModule** - Enhanced patient orientation
- [ ] **SynchronizationModule** - Synchronization data
- [ ] **SpecimenModule** - Specimen information

## Complete Module Implementation Checklist (44 Total)

**All 44 modules from the DICOM standard documentation:**

1. [x] **approval** - Approval information
2. [ ] **cine** - Cine image information
3. [x] **clinical_trial_series** - ✅ Complete with coordinating center info
4. [x] **clinical_trial_study** - ✅ Complete with temporal event handling
5. [x] **clinical_trial_subject** - ✅ Complete with conditional validation
6. [ ] **common_instance_reference** - Instance references
7. [ ] **contrast_bolus** - Contrast bolus information
8. [ ] **ct_image** - CT image specific data
9. [ ] **device** - Device information
10. [ ] **enhanced_patient_orientation** - Enhanced patient orientation
11. [ ] **frame_extraction** - Frame extraction data
12. [ ] **frame_of_reference** - Spatial reference frame
13. [ ] **general_acquisition** - General acquisition parameters
14. [ ] **general_equipment** - Equipment/device information
15. [ ] **general_image** - General image information
16. [ ] **general_reference** - General reference data
17. [x] **general_series** - ✅ Complete with positioning and operator info
18. [x] **general_study** - ✅ Complete with physician identification
19. [ ] **image_pixel** - Pixel data characteristics
20. [ ] **image_plane** - Image geometry and orientation
21. [ ] **modality_lut** - Modality LUT data
22. [ ] **multi-energy_ct_image** - Multi-energy CT image data
23. [ ] **multi-frame** - Multi-frame image data
24. [ ] **overlay_plane** - Overlay plane data
25. [x] **patient** - ✅ Complete exemplar implementation with BaseModule inheritance
26. [x] **patient_study** - ✅ Complete with comprehensive study attributes
27. [ ] **roi_contour** - ROI contour data
28. [ ] **rt_beams** - RT beam information
29. [ ] **rt_brachy_application_setups** - Brachytherapy application setups
30. [ ] **rt_dose** - Dose distribution data
31. [ ] **rt_dvh** - Dose-volume histogram
32. [ ] **rt_fraction_scheme** - RT fraction scheme
33. [ ] **rt_general_plan** - Basic RT plan information
34. [ ] **rt_image** - RT image information
35. [ ] **rt_patient_setup** - RT patient setup
36. [ ] **rt_prescription** - RT prescription
37. [ ] **rt_roi_observations** - RT ROI observations
38. [x] **rt_series** - ✅ Complete with RT-specific elements
39. [ ] **rt_tolerance_tables** - RT tolerance tables
40. [ ] **sop_common** - SOP instance identification
41. [ ] **specimen** - Specimen information
42. [ ] **structure_set** - Structure set information
43. [ ] **synchronization** - Synchronization data
44. [ ] **voi_lut** - VOI LUT data

## Module Testing Standards

**Each module must pass comprehensive testing before implementation completion:**

### 🧪 Unit Tests (Required for Each Module)
- [ ] **Factory Method Tests**: `from_required_elements()` with valid/invalid Type 1/2 parameters
- [ ] **Builder Method Tests**: All `with_*()` methods with valid/invalid optional parameters
- [ ] **Static Helper Tests**: `create_*_item()` methods for sequence item construction
- [ ] **Property Tests**: All `is_*` and `has_*` properties return expected boolean values
- [ ] **Validation Tests**: `validate()` method returns proper error/warning structure

### 🎯 IntelliSense Verification (Manual Testing)
- [ ] **Method Visibility**: Only factory, builder, helper, and property methods visible
- [ ] **Parameter Clarity**: All DICOM elements clearly named as method parameters
- [ ] **Type Hints**: Full IDE autocomplete with parameter types and return values
- [ ] **Documentation**: Method docstrings show DICOM tags and element types

### ✅ DICOM Compliance Testing  
- [ ] **Dataset Generation**: Module creates valid pydicom.Dataset with proper DICOM attributes
- [ ] **Tag Validation**: All DICOM tags match PS3.6 data dictionary
- [ ] **VR Compliance**: Value representations match DICOM standard requirements
- [ ] **Type Requirements**: Type 1/2/3/1C/2C elements handled according to DICOM rules

## Implementation Dependencies

**Build modules in logical dependency order:**

### 🥇 Priority 1 - Foundation Modules
1. **PatientModule** - ✅ Complete (exemplar implementation)
2. **GeneralStudyModule** - Study-level identification 
3. **GeneralEquipmentModule** - Equipment/manufacturer information
4. **SOPCommonModule** - SOP instance identification

### 🥈 Priority 2 - Series & Image Modules  
5. **GeneralSeriesModule** - Series-level information
6. **RTSeriesModule** - RT-specific series information
7. **FrameOfReferenceModule** - Spatial coordinate system
8. **GeneralImageModule** - Basic image information

### 🥉 Priority 3 - Specialized Modules
9. **RTDoseModule** - Dose distribution data
10. **ImagePixelModule** - Pixel data characteristics
11. **ImagePlaneModule** - Image geometry and positioning
12. Additional modules as needed...

**Note**: Each module is self-contained and can be implemented independently following the PatientModule pattern.