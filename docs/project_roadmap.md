# PyRT-DICOM Project Roadmap

## Project Overview

**PyRT-DICOM** is a Python library for creating radiotherapy DICOM files with CT, RTImage, RTDose, RTPlan, and RTStruct modalities. Built on top of pydicom, this library provides a strongly-typed, IntelliSense-friendly interface for creating DICOM Information Object Definitions (IODs) through modular composition.

### Core Design Principles

1. **Modular Architecture**: IODs are composed from individual modules
2. **Type Safety**: All DICOM data elements as class attributes for IntelliSense
3. **No Free Text Configuration**: Strongly-typed interfaces with strict enum enforcement
4. **Test-Driven Development**: Pytest tests implemented alongside features
5. **Progressive Development**: Start with POC, validate approach, then scale
6. **User-Friendly API**: Prioritize ease of use over performance optimization
7. **Optional Validation**: No validation at creation, validate on-demand or during export
8. **Auto-Generated UIDs**: UIDs generated at object creation with cross-dataset linking
9. **Memory Storage**: Store all data in memory for fast access and simplicity

## Phase 1: Foundation & Proof of Concept (Weeks 1-2)

### 1.1 Project Structure Setup
- [ ] Initialize project with proper Python package structure
- [ ] Set up pytest testing framework
- [ ] Configure GitHub Actions for CI/CD
- [ ] Create development environment setup documentation
- [ ] Set up pre-commit hooks for code quality

### 1.2 Core Infrastructure
- [ ] **BaseModule**: Abstract base class for all DICOM modules with validate() method
- [ ] **BaseIOD**: Abstract base class for all IOD implementations with validate(), create_dataset(), and save_dataset() methods
- [ ] **ValidationResult**: Class to collect validation errors and warnings with detailed reports
- [ ] **UIDGenerator**: Utility for generating valid DICOM UIDs at object creation
- [ ] **Common Enumerations**: Strict DICOM enums and constants (no free text)
- [ ] **DatasetLinker**: Utility for linking datasets across studies with UID management

### 1.3 POC Implementation (Essential Modules Only)
**Target**: Create a minimal working RT Plan IOD

#### Essential Modules for POC:
- [ ] **PatientModule**: Patient identification and demographics
- [ ] **GeneralStudyModule**: Study-level information
- [ ] **GeneralEquipmentModule**: Equipment/device information
- [ ] **RTSeriesModule**: RT-specific series information
- [ ] **RTGeneralPlanModule**: Basic plan information
- [ ] **SOPCommonModule**: SOP instance identification

#### POC IOD Implementation:
- [ ] **RTPlanIOD**: Minimal RT Plan implementation
  - `RTPlan.from_required_modules(patient, study, equipment, series, plan, sop)`
  - `rt_plan.add_patient_module(patient_module)`
  - `rt_plan.validate()` - Returns ValidationResult with errors/warnings
  - `rt_plan.create_dataset(validate=True)` - Creates pydicom Dataset
  - `rt_plan.save_dataset(filepath, validate=True)` - Saves DICOM file

### 1.4 POC Validation
- [ ] Unit tests for all POC modules
- [ ] Integration test creating valid RT Plan DICOM file
- [ ] DICOM validation using external tools (dcmtk, pydicom)
- [ ] User experience testing with stakeholders

**Milestone**: Working POC that creates valid RT Plan DICOM file

## Phase 2: Core Module Library (Weeks 3-6)

### 2.1 Universal Modules (Common to All IODs)
- [ ] **ClinicalTrialSubjectModule**
- [ ] **PatientStudyModule**
- [ ] **ClinicalTrialStudyModule**
- [ ] **ClinicalTrialSeriesModule**
- [ ] **FrameOfReferenceModule**
- [ ] **CommonInstanceReferenceModule**
- [ ] **GeneralReferenceModule**

### 2.2 Image-Related Modules
- [ ] **GeneralImageModule**
- [ ] **ImagePlaneModule**
- [ ] **ImagePixelModule**
- [ ] **MultiFrameModule**
- [ ] **ContrastBolusModule**
- [ ] **DeviceModule**
- [ ] **VOILUTModule**
- [ ] **ModalityLUTModule**

### 2.3 CT-Specific Modules
- [ ] **GeneralSeriesModule** (CT variant)
- [ ] **GeneralAcquisitionModule**
- [ ] **CTImageModule**
- [ ] **MultiEnergyCTImageModule**
- [ ] **EnhancedPatientOrientationModule**
- [ ] **SpecimenModule**
- [ ] **OverlayPlaneModule**
- [ ] **SynchronizationModule**

### 2.4 RT-Specific Modules
- [ ] **RTDoseModule**
- [ ] **RTDVHModule**
- [ ] **RTImageModule**
- [ ] **RTToleranceTablesModule**
- [ ] **RTPatientSetupModule**
- [ ] **RTFractionSchemeModule**
- [ ] **RTBeamsModule**
- [ ] **RTBrachyApplicationSetupsModule**
- [ ] **StructureSetModule**
- [ ] **ROIContourModule**
- [ ] **RTROIObservationsModule**
- [ ] **RTPrescriptionModule**
- [ ] **ApprovalModule**

### 2.5 Additional Modules
- [ ] **CineModule**
- [ ] **FrameExtractionModule**

**Milestone**: Complete module library with comprehensive tests

## Phase 3: IOD Implementations (Weeks 7-10)

### 3.1 CT Image IOD
```python
# Target Usage:
ct_image = CTImage.from_required_modules(
    patient=patient_module,
    study=general_study_module,
    series=general_series_module,
    frame_of_reference=frame_of_reference_module,
    equipment=general_equipment_module,
    acquisition=general_acquisition_module,
    image=general_image_module,
    image_plane=image_plane_module,
    image_pixel=image_pixel_module,
    ct_image=ct_image_module,
    sop_common=sop_common_module
)
ct_image.add_contrast_bolus_module(contrast_module)  # Optional
```

### 3.2 RT Image IOD
```python
# Target Usage:
rt_image = RTImage.from_required_modules(
    patient=patient_module,
    study=general_study_module,
    series=rt_series_module,
    equipment=general_equipment_module,
    acquisition=general_acquisition_module,
    image=general_image_module,
    image_pixel=image_pixel_module,
    rt_image=rt_image_module,
    sop_common=sop_common_module
)
```

### 3.3 RT Dose IOD
```python
# Target Usage:
rt_dose = RTDose.from_required_modules(
    patient=patient_module,
    study=general_study_module,
    series=rt_series_module,
    frame_of_reference=frame_of_reference_module,
    equipment=general_equipment_module,
    rt_dose=rt_dose_module,
    sop_common=sop_common_module
)
rt_dose.add_grid_based_dose_modules(image, image_plane, image_pixel)  # Conditional
```

### 3.4 RT Plan IOD (Enhanced from POC)
```python
# Target Usage:
rt_plan = RTPlan.from_required_modules(
    patient=patient_module,
    study=general_study_module,
    series=rt_series_module,
    equipment=general_equipment_module,
    rt_general_plan=rt_general_plan_module,
    sop_common=sop_common_module
)
rt_plan.add_beams_module(rt_beams_module)  # Conditional
rt_plan.add_fraction_scheme_module(rt_fraction_scheme_module)  # Optional
```

### 3.5 RT Structure Set IOD
```python
# Target Usage:
rt_struct = RTStructureSet.from_required_modules(
    patient=patient_module,
    study=general_study_module,
    series=rt_series_module,
    equipment=general_equipment_module,
    structure_set=structure_set_module,
    roi_contour=roi_contour_module,
    rt_roi_observations=rt_roi_observations_module,
    sop_common=sop_common_module
)
```

**Milestone**: All 5 IOD implementations complete with comprehensive tests

## Phase 4: Advanced Features & Optimization (Weeks 11-12)

### 4.1 Validation & Compliance
- [ ] **ValidationResult System**: Comprehensive error and warning collection with detailed reports
- [ ] **DICOM Standard Compliance Checker**: Validate against PS3.3 requirements
- [ ] **Cross-Reference Validation**: Ensure referenced UIDs exist and are valid
- [ ] **Conditional Module Validation**: Enforce mutually exclusive and conditional requirements
- [ ] **Data Element Validation**: Type checking, value range validation, enum enforcement
- [ ] **Warning System**: Differentiate between critical errors and informational warnings

### 4.2 Utility Functions
- [ ] **UID Generation**: Auto-generate valid DICOM UIDs at object creation
- [ ] **Dataset Linking**: Link datasets across studies with UID management
- [ ] **Date/Time Utilities**: DICOM date/time formatting helpers
- [ ] **Coordinate System Utilities**: Spatial reference calculations
- [ ] **Import/Export Helpers**: Convert from/to pydicom Dataset
- [ ] **Synthetic Test Data**: Integration with pydicom test datasets

### 4.3 Documentation & Examples
- [ ] **API Documentation**: Comprehensive Sphinx documentation
- [ ] **Usage Examples**: Real-world usage scenarios
- [ ] **Migration Guide**: From raw pydicom to PyRT-DICOM
- [ ] **DICOM Standard Mapping**: Reference documentation

### 4.4 Performance & Quality
- [ ] **Performance Benchmarks**: Memory and speed optimization
- [ ] **Code Coverage**: Achieve >95% test coverage
- [ ] **Type Hints**: Full mypy compliance
- [ ] **Linting & Formatting**: Black, flake8, isort integration

**Milestone**: Production-ready library with comprehensive documentation

## Development Guidelines

### Code Quality Standards

#### Module Implementation Pattern
```python
from typing import Optional
from pydicom import Dataset
from .base import BaseModule
from .enums import PatientSex
from .validation import ValidationResult
from .uid_generator import generate_uid

class PatientModule(BaseModule):
    """Patient identification and demographic information.
    
    Implements DICOM PS3.3 C.7.1.1 Patient Module.
    
    Args:
        patient_name: Patient's full name (Type 2 - Required, may be empty)
        patient_id: Primary identifier for the patient (Type 2)
        patient_birth_date: Patient's birth date (Type 2)
        patient_sex: Patient's sex (Type 2)
        
    Example:
        >>> patient = PatientModule(
        ...     patient_name="Doe^John^^^",
        ...     patient_id="12345",
        ...     patient_birth_date="19800101",
        ...     patient_sex=PatientSex.MALE
        ... )
        >>> validation_result = patient.validate()
        >>> if validation_result.is_valid:
        ...     ds = patient.to_dataset()
    """
    
    def __init__(
        self,
        patient_name: str,
        patient_id: str,
        patient_birth_date: str = "",
        patient_sex: Optional[PatientSex] = None,
        **kwargs
    ):
        super().__init__(**kwargs)
        
        # Store values without validation - validation happens on-demand
        self.patient_name = patient_name
        self.patient_id = patient_id
        self.patient_birth_date = patient_birth_date
        self.patient_sex = patient_sex
    
    def validate(self) -> ValidationResult:
        """Validate module data and return detailed results."""
        result = ValidationResult()
        
        # Validate person name format
        if not self._is_valid_person_name(self.patient_name):
            result.add_error("Patient Name must follow DICOM Person Name format")
        
        # Validate date format
        if self.patient_birth_date and not self._is_valid_date(self.patient_birth_date):
            result.add_error("Patient Birth Date must be in YYYYMMDD format")
            
        # Add warnings for optional but recommended fields
        if not self.patient_birth_date:
            result.add_warning("Patient Birth Date is empty - recommended for clinical use")
            
        return result
    
    def to_dataset(self) -> Dataset:
        """Convert module to pydicom Dataset."""
        ds = Dataset()
        ds.PatientName = self.patient_name
        ds.PatientID = self.patient_id
        if self.patient_birth_date:
            ds.PatientBirthDate = self.patient_birth_date
        if self.patient_sex:
            ds.PatientSex = self.patient_sex.value
        return ds
```

#### IOD Implementation Pattern
```python
from typing import Optional, List
from pathlib import Path
from pydicom import Dataset
from .base import BaseIOD
from .modules import PatientModule, GeneralStudyModule, SOPCommonModule
from .validation import ValidationResult
from .uid_generator import generate_uid

class RTPlan(BaseIOD):
    """RT Plan Information Object Definition.
    
    Implements DICOM PS3.3 A.20 RT Plan IOD.
    
    Example:
        >>> rt_plan = RTPlan.from_required_modules(
        ...     patient=patient_module,
        ...     study=study_module,
        ...     # ... other required modules
        ... )
        >>> # Validate before export
        >>> validation = rt_plan.validate()
        >>> if validation.has_errors:
        ...     print(validation.get_error_report())
        ... else:
        ...     rt_plan.save_dataset("plan.dcm")
    """
    
    SOP_CLASS_UID = "1.2.840.10008.*******.1.481.5"  # RT Plan Storage
    
    def __init__(self):
        super().__init__()
        self._patient_module: Optional[PatientModule] = None
        self._general_study_module: Optional[GeneralStudyModule] = None
        self._sop_instance_uid = generate_uid()  # Auto-generate UID
        # ... other modules
    
    @classmethod
    def from_required_modules(
        cls,
        patient: PatientModule,
        study: GeneralStudyModule,
        series: RTSeriesModule,
        equipment: GeneralEquipmentModule,
        rt_general_plan: RTGeneralPlanModule,
        sop_common: SOPCommonModule
    ) -> 'RTPlan':
        """Create RT Plan from required modules."""
        instance = cls()
        instance._patient_module = patient
        instance._general_study_module = study
        # ... set other required modules
        # No validation at creation time
        return instance
    
    def add_patient_module(self, patient: PatientModule) -> None:
        """Add or update patient module."""
        self._patient_module = patient
    
    def validate(self) -> ValidationResult:
        """Validate entire IOD and return detailed results."""
        result = ValidationResult()
        
        # Check required modules
        if not self._patient_module:
            result.add_error("Patient Module is required")
        else:
            # Validate individual modules
            module_validation = self._patient_module.validate()
            result.merge(module_validation)
        
        # Check conditional requirements (e.g., RT Beams XOR RT Brachy)
        self._validate_conditional_modules(result)
        
        return result
    
    def create_dataset(self, validate: bool = True) -> Dataset:
        """Create pydicom Dataset with optional validation."""
        if validate:
            validation = self.validate()
            if validation.has_errors:
                raise ValueError(f"Validation failed: {validation.get_error_report()}")
        
        ds = Dataset()
        ds.SOPClassUID = self.SOP_CLASS_UID
        ds.SOPInstanceUID = self._sop_instance_uid
        
        # Combine all module datasets
        if self._patient_module:
            ds.update(self._patient_module.to_dataset())
        # ... combine other modules
        
        return ds
    
    def save_dataset(self, filepath: Path | str, validate: bool = True) -> None:
        """Save IOD as DICOM file with optional validation."""
        ds = self.create_dataset(validate=validate)
        ds.save_as(str(filepath))
```

### Testing Strategy

#### Unit Tests
```python
import pytest
from pyrt_dicom.modules import PatientModule
from pyrt_dicom.enums import PatientSex

class TestPatientModule:
    def test_required_fields(self):
        """Test module creation with required fields only."""
        patient = PatientModule(
            patient_name="Doe^John^^^",
            patient_id="12345"
        )
        assert patient.patient_name == "Doe^John^^^"
        assert patient.patient_id == "12345"
    
    def test_to_dataset_conversion(self):
        """Test conversion to pydicom Dataset."""
        patient = PatientModule(
            patient_name="Doe^John^^^",
            patient_id="12345",
            patient_birth_date="19800101",
            patient_sex=PatientSex.MALE
        )
        ds = patient.to_dataset()
        
        assert ds.PatientName == "Doe^John^^^"
        assert ds.PatientID == "12345"
        assert ds.PatientBirthDate == "19800101"
        assert ds.PatientSex == "M"
```

#### Integration Tests
```python
def test_rt_plan_creation_and_export():
    """Test complete RT Plan creation and DICOM export."""
    # Create modules
    patient = PatientModule(patient_name="Test^Patient", patient_id="001")
    study = GeneralStudyModule(study_instance_uid="1.2.3.4.5")
    # ... create other required modules
    
    # Create RT Plan
    rt_plan = RTPlan.from_required_modules(
        patient=patient,
        study=study,
        # ... other modules
    )
    
    # Convert to DICOM
    ds = rt_plan.to_pydicom_dataset()
    
    # Validate DICOM compliance
    assert hasattr(ds, 'PatientName')
    assert hasattr(ds, 'StudyInstanceUID')
    assert ds.SOPClassUID == rt_plan.SOP_CLASS_UID
    
    # Test file I/O
    ds.save_as("test_rt_plan.dcm")
    # Validate with external tool
```

### Git Workflow
- **Feature Branches**: Each module/IOD gets its own feature branch
- **Frequent Commits**: Commit after each module/test completion
- **Pull Requests**: All changes via PR with code review
- **Semantic Versioning**: Follow semver for releases
- **Changelog**: Maintain detailed changelog

### Documentation Standards
- **Google-style Docstrings**: All classes and methods
- **Type Hints**: Complete type annotations
- **Examples**: Real-world usage examples in docstrings
- **API Reference**: Auto-generated Sphinx documentation

## Risk Analysis & Mitigation

### Technical Risks
1. **DICOM Compliance**: Mitigation - extensive validation testing
2. **Performance**: Mitigation - benchmark testing, lazy loading
3. **Complexity**: Mitigation - POC validation, modular architecture

### Project Risks
1. **Scope Creep**: Mitigation - phased approach, clear milestones
2. **User Adoption**: Mitigation - stakeholder feedback on POC
3. **Maintenance**: Mitigation - comprehensive tests, documentation

## Success Metrics

### Phase 1 (POC) Success Criteria
- [ ] Create valid RT Plan DICOM file
- [ ] Positive stakeholder feedback on API design
- [ ] All POC tests passing
- [ ] Documentation covers POC usage

### Final Success Criteria
- [ ] All 5 IOD types implemented and tested
- [ ] >95% test coverage
- [ ] Complete API documentation
- [ ] Performance benchmarks meet targets
- [ ] User feedback confirms ease of use

## Next Steps - POC Focus

1. **Initialize with Hatch**: `hatch new pyrt-dicom` in current directory
2. **Create POC Modules**: Implement concrete PatientModule, RTDoseModule, etc. (NO base classes)
3. **Test Module Inheritance**: Does RTDose(PatientModule, RTDoseModule, ...) work well?
4. **Validate User Experience**: Can users easily access all DICOM data elements?
5. **Create Valid DICOM**: Generate compliant RT Dose files
6. **Measure Success**: Meet all POC success criteria before any refactoring

**⚠️ REMEMBER: NO ABSTRACTIONS UNTIL POC PROVES THE APPROACH WORKS ⚠️**

---

*This roadmap serves as a living document and will be updated as the project progresses and requirements evolve.*